# Coding Standards

## TypeScript Guidelines

### Type Safety

- **Always use strict mode**: TypeScript strict configuration is enabled
- **Explicit types**: Prefer explicit type annotations for function parameters and return types
- **No any types**: Avoid `any` - use proper types or `unknown` with type guards
- **Interface over type**: Use interfaces for object shapes, types for unions/primitives
- **Readonly when appropriate**: Use `readonly` for immutable data structures

### Code Organization

- **Single responsibility**: Each module should have one clear purpose
- **Dependency injection**: Pass dependencies as parameters rather than importing directly
- **Error handling**: Use custom error classes (ConvertAppsError) with specific error types
- **Async/await**: Prefer async/await over Promises for better readability

### Naming Conventions

- **Functions**: camelCase with descriptive verbs (`discoverApps`, `installPackages`)
- **Variables**: camelCase with descriptive nouns (`selectedApps`, `installationResult`)
- **Constants**: UPPER_SNAKE_CASE (`EXIT_CODES`, `BREW_COMMANDS`)
- **Types/Interfaces**: PascalCase (`AppInfo`, `InstallationResult`)
- **Files**: kebab-case for modules (`app-scanner.ts`, `error-handler.ts`)

### Module Structure

```typescript
// 1. External imports
import { exec } from 'child_process';
import { promises as fs } from 'fs';

// 2. Internal imports
import { AppInfo, ScannerConfig } from './types.ts';
import { createLogger } from './utils.ts';

// 3. Type definitions (if any)
interface LocalType {
  // ...
}

// 4. Constants
const LOCAL_CONSTANTS = {
  // ...
};

// 5. Helper functions
function helperFunction() {
  // ...
}

// 6. Main exported functions
export async function mainFunction() {
  // ...
}
```

## Error Handling Patterns

### Custom Error Classes

- Use `ConvertAppsError` with specific `ErrorType` enum values
- Include original error in error chain when wrapping
- Provide context-specific error messages
- Include recovery suggestions in error handling

### Error Recovery

- Always provide actionable error messages
- Include troubleshooting steps for common issues
- Use appropriate exit codes for different error scenarios
- Log errors with appropriate verbosity levels

### Validation

- Validate inputs at module boundaries
- Use type guards for runtime type checking
- Fail fast with clear error messages
- Provide default values where appropriate

## Testing Standards

### Test Organization

- One test file per source file (`module.test.ts` for `module.ts`)
- Group tests by functionality using `describe` blocks
- Use descriptive test names that explain the scenario
- Follow AAA pattern: Arrange, Act, Assert

### Mocking Strategy

- Mock external dependencies (fs, child_process, inquirer)
- Use Jest's built-in mocking capabilities
- Mock at module boundaries, not internal functions
- Provide realistic mock data that matches actual usage

### Coverage Requirements

- Maintain >70% statement coverage
- Focus on critical paths and error scenarios
- Test both success and failure cases
- Include integration tests for complete workflows

## Performance Considerations

### Async Operations

- Use batch operations where possible (brew install multiple packages)
- Implement proper timeout handling for long-running commands
- Provide progress indicators for operations >5 seconds
- Use throttling for frequent updates (progress tracking)

### Memory Management

- Avoid loading large files into memory
- Stream data when processing large outputs
- Clean up resources in finally blocks
- Use appropriate data structures for the task

## Security Guidelines

### Input Validation

- Sanitize all user inputs before using in shell commands
- Use `escapeShellArg` utility for shell command construction
- Validate file paths to prevent directory traversal
- Check file permissions before operations

### Credential Handling

- Never log passwords or sensitive information
- Use secure prompts for password input
- Clear sensitive data from memory when possible
- Validate sudo access before attempting privileged operations

### File Operations

- Check file existence before operations
- Use appropriate file permissions
- Handle permission errors gracefully
- Provide clear feedback for permission issues
