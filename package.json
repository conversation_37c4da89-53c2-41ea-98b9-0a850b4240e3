{"name": "convert-apps-to-homebrew", "version": "1.0.0", "description": "Convert macOS applications to Homebrew installations with interactive selection", "keywords": ["homebrew", "macos", "applications", "cli", "package-manager", "automation", "typescript", "interactive", "cask", "formula", "installer", "migration", "apps", "brew"], "homepage": "https://github.com/deepfriedmind/convert-apps-to-homebrew#readme", "bugs": {"url": "https://github.com/deepfriedmind/convert-apps-to-homebrew/issues"}, "repository": {"type": "git", "url": "https://github.com/deepfriedmind/convert-apps-to-homebrew.git"}, "license": "MIT", "author": "deepfriedmind", "type": "module", "main": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "bin": {"convert-apps-to-homebrew": "dist/index.js"}, "files": ["CHANGELOG.md", "LICENSE", "README.md", "dist/**/*"], "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint", "lint:fix": "eslint --fix", "prepublishOnly": "npm run build && npm test", "start": "tsc && node dist/index.js", "test": "jest", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "typecheck": "tsc --noEmit"}, "dependencies": {"@inquirer/checkbox": "^4.1.8", "@inquirer/password": "^4.0.15", "commander": "^14.0.0"}, "devDependencies": {"@antfu/eslint-config": "^4.13.2", "@tsconfig/node-ts": "^23.6.1", "@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.29", "eslint": "^9.28.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-perfectionist": "^4.13.0", "typescript": "^5.8.3"}, "packageManager": "npm@11.4.1", "engines": {"node": ">= 22", "npm": ">= 11"}, "os": ["darwin"]}